
server {
    listen 80;
    server_name localhost;
    root /var/www/html/public;

    index index.php;

    charset utf-8;

    gzip on;
    gzip_types text/plain text/css application/json ap`plication/javascript text/xml application/xml application/xml+rss text/javascript;
    gzip_min_length 1000;
    gzip_comp_level 6;

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location = /favicon.ico { access_log off; log_not_found off; }
    location = /robots.txt  { access_log off; log_not_found off; }

    location ~* \.(jpg|jpeg|png|gif|ico|css|js)$ {
        expires 30d;
        add_header Cache-Control "public, no-transform";
    }

    error_page 404 /index.php;

location ~ \.php$ {
    fastcgi_pass gnb-caribe-api-backend:9000;
    fastcgi_index index.php;
    include fastcgi_params;
    fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
}


    location ~ /\.(?!well-known).* {
        deny all;
    }
}
