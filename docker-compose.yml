services:
    app:
        build:
            context: .
            dockerfile: Dockerfile
            args:
                USER_ID: ${USER_ID:-1000}
                GROUP_ID: ${GROUP_ID:-1000}
                USER_NAME: ${USER_NAME:-laravel}
                GROUP_NAME: ${GROUP_NAME:-laravel}
        container_name: ${APP_NAME}-backend
        restart: unless-stopped
        environment:
            USER_ID: ${USER_ID:-1000}
            GROUP_ID: ${GROUP_ID:-1000}
            USER_NAME: ${USER_NAME:-laravel}
            GROUP_NAME: ${GROUP_NAME:-laravel}
        volumes:
            - ./:/var/www/html
        networks:
            - app-network
        depends_on:
            - db

    supervisord:
        build:
            context: ./docker/supervisord
            dockerfile: Dockerfile
            args:
                USER_ID: ${USER_ID:-1000}
                GROUP_ID: ${GROUP_ID:-1000}
                USER_NAME: ${USER_NAME:-laravel}
                GROUP_NAME: ${GROUP_NAME:-laravel}
        container_name: ${APP_NAME}-supervisord
        restart: unless-stopped
        environment:
            USER_ID: ${USER_ID:-1000}
            GROUP_ID: ${GROUP_ID:-1000}
            USER_NAME: ${USER_NAME:-laravel}
            GROUP_NAME: ${GROUP_NAME:-laravel}
        volumes:
            - ./:/var/www/html
        networks:
            - app-network
        depends_on:
            - app
            - db

    nginx:
        build:
            context: ./docker/nginx
            dockerfile: Dockerfile
        container_name: ${APP_NAME}-nginx
        restart: unless-stopped
        ports:
            - ${APP_PORT:-80}:80
        volumes:
            - ./:/var/www/html
        networks:
            - app-network
        depends_on:
            - app

    db:
        image: postgres:16.2-alpine
        container_name: ${APP_NAME}-db
        restart: unless-stopped
        environment:
            POSTGRES_DB: ${DB_DATABASE:-laravel}
            POSTGRES_USER: ${DB_USERNAME:-postgres}
            POSTGRES_PASSWORD: ${DB_PASSWORD:-secret}
        ports:
            - ${FORWARD_DB_PORT:-5432}:5432
        volumes:
            - postgres_data:/var/lib/postgresql/data
        networks:
            - app-network

networks:
    app-network:
        driver: bridge

volumes:
    postgres_data:
