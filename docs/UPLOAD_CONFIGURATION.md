# Configuración de Uploads para Archivos Grandes y Livewire

## Problema Resuelto

Este documento describe la solución implementada para resolver el error **403 Forbidden** que ocurría al:
- Enviar archivos base64 muy grandes en producción
- Usar uploads de Livewire con archivos grandes
- Procesar formularios con muchos campos

## Cambios Implementados

### 1. Configuración de Nginx (`docker/nginx/nginx.conf`)

**Límites aumentados:**
- `client_max_body_size`: 2048M (antes 500M)
- `client_body_timeout`: 3600s (antes 300s)
- `client_header_buffer_size`: 32k (antes 1k)
- `large_client_header_buffers`: 8 128k (antes 4 4k)

**Configuraciones adicionales:**
- `client_body_in_file_only clean`: Evita problemas con archivos grandes
- `client_body_temp_path /tmp/nginx-client-body`: Directorio temporal específico
- Buffers FastCGI aumentados significativamente
- Timeouts extendidos para requests largos

**Ubicaciones específicas:**
- `/livewire`: Configuración optimizada para Livewire
- `/admin`: Configuración para Filament/Admin
- `/upload/`: Límites específicos para uploads

### 2. Configuración de PHP (`docker/php/production.ini`)

**Límites de archivos:**
- `upload_max_filesize`: 2048M
- `post_max_size`: 2048M
- `max_file_uploads`: 200
- `max_input_vars`: 50000

**Límites de tiempo y memoria:**
- `memory_limit`: 2048M
- `max_execution_time`: 3600s
- `max_input_time`: 3600s

**Optimizaciones de OPcache:**
- Configuración optimizada para producción
- Cache de realpath aumentado

### 3. Configuración de Docker

**Dockerfile actualizado:**
- Copia automática de configuración de PHP optimizada
- Configuración consistente entre contenedores

**Nginx Dockerfile:**
- Creación automática de directorios temporales
- Permisos correctos para nginx

## Verificación de la Configuración

### Script de Verificación

Ejecuta el script de verificación para comprobar que todo esté configurado correctamente:

```bash
php scripts/check-upload-config.php
```

Este script verifica:
- Configuraciones de PHP
- Extensiones necesarias
- Límites en bytes
- Consistencia entre configuraciones
- Espacio en disco disponible

### Verificación Manual

1. **Verificar configuración de PHP:**
```bash
docker exec -it tu-app-backend php -i | grep -E "(upload_max_filesize|post_max_size|memory_limit)"
```

2. **Verificar configuración de Nginx:**
```bash
docker exec -it tu-app-nginx nginx -T | grep -E "(client_max_body_size|client_body_timeout)"
```

3. **Verificar logs de errores:**
```bash
docker logs tu-app-nginx
docker logs tu-app-backend
```

## Despliegue

### Reconstruir Contenedores

Después de aplicar estos cambios, reconstruye los contenedores:

```bash
# Detener contenedores
docker-compose down

# Reconstruir imágenes
docker-compose build --no-cache

# Iniciar contenedores
docker-compose up -d
```

### Verificar Funcionamiento

1. **Test de upload pequeño:**
```bash
curl -X POST -F "file=@small-file.txt" http://tu-dominio/upload/test
```

2. **Test de Livewire:**
- Accede a una página con componente Livewire
- Intenta subir un archivo grande (>100MB)
- Verifica que no aparezca error 403

## Monitoreo

### Logs a Revisar

1. **Nginx Error Log:**
```bash
docker exec -it tu-app-nginx tail -f /var/log/nginx/error.log
```

2. **PHP Error Log:**
```bash
docker exec -it tu-app-backend tail -f /var/www/html/storage/logs/laravel.log
```

### Métricas Importantes

- Tiempo de respuesta de uploads
- Uso de memoria durante uploads
- Espacio en disco temporal
- Errores 413 (Request Entity Too Large)
- Errores 403 (Forbidden)

## Troubleshooting

### Error 413 Request Entity Too Large
- Verificar `client_max_body_size` en Nginx
- Verificar que todos los location blocks tengan el límite correcto

### Error 403 Forbidden
- Verificar permisos del directorio temporal
- Verificar `client_body_temp_path`
- Verificar logs de Nginx para detalles específicos

### Timeout en Uploads
- Aumentar `client_body_timeout`
- Aumentar `fastcgi_read_timeout`
- Verificar `max_execution_time` en PHP

### Memoria Insuficiente
- Aumentar `memory_limit` en PHP
- Verificar uso de memoria del contenedor
- Considerar usar streaming para archivos muy grandes

## Configuración para Diferentes Entornos

### Desarrollo Local
Los límites pueden ser menores para desarrollo:
- `client_max_body_size`: 100M
- `memory_limit`: 512M
- `max_execution_time`: 300s

### Producción
Usar los valores implementados en este documento para máximo rendimiento.

### Staging
Usar configuración similar a producción para testing realista.

## Notas Importantes

1. **Espacio en Disco:** Asegúrate de tener suficiente espacio en `/tmp` para archivos temporales
2. **Memoria RAM:** Los límites altos de memoria requieren suficiente RAM en el servidor
3. **Seguridad:** Los límites altos pueden ser un vector de ataque DoS, monitorea el uso
4. **Performance:** Archivos muy grandes pueden afectar el rendimiento, considera usar queues para procesamiento asíncrono
